﻿using FluentValidation.Results;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.CustomAttribute;
using WHO.MALARIA.Domain.Dtos.OutputDtos;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Helper;
using static WHO.MALARIA.Domain.Constants.Constants;

namespace WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_3.Indicator_3_6_1
{
    /// <summary>
    /// Contains desk review response properties for Indicator 3.6.1
    /// </summary>
    public class Response_1 : AssessmentResponseBase, IResponseValidator
    {
        public bool CannotBeAssessed { get; set; }

        public string CannotBeAssessedReason { get; set; }

        public string MetNotMetStatus { get; set; }

        public List<AccessUser> AccessUsers { get; set; }

        public bool CanAccessNational { get; set; }

        public bool CanAccessSubNational { get; set; }

        public bool CanAccessServiceDelivery { get; set; }

        /// <summary>
        /// Validates indicator 3.6.1
        /// </summary>
        /// <returns>Validation results for indicator 3.6.1</returns>
        public ValidationResult Validate()
        {
            return new Response_1_Validator().Validate(this);
        }

        /// <summary>
        /// Get analytical report along with the documents for the indicator 3.6.1
        /// </summary>
        /// <param name="translator">Delegate object which is used for translation</param>     
        /// <returns>Object of analytical output indicator response dto</returns>
        public AnalyticalOutputIndicatorResponseDto BuildReportResponse(Delegate translator)
        {
            List<AccessUser> accessUsers = GetAccessUserData(translator);

            string pecentage = CalculateNullPercentageInArray();

            accessUsers.Add(new AccessUser
            {
                Name = translator.DynamicInvoke(AnalyticalOutputConstants.ProportionOfHealthSystemLevels_3_6_1).ToString(),
                ServiceDelivery = pecentage,
                National = "",
                SubNational = "",
                DataAccessDetails = ""
            });

            AnalyticalOutputType outputType = AnalyticalOutputType.Table;

            TableResponse table = AnalyticalOutputIndicatorResponseHelper.GetAnalyticalOutputIndicatorTable(typeof(AccessUser), accessUsers, translator);

            table.HasCalculation = true;

            AnalyticalOutputIndicatorResponseDto response = new AnalyticalOutputIndicatorResponseDto
            {
                Type = (int)outputType,
                Response = table
            };

            return response;
        }

        /// <summary>
        ///  Process indicator response and produce the result that can be exported
        /// </summary>
        /// <param name="translator">Delegate object which is used for translation</param>        
        /// <param name="indicatorSequence">Contains name of indicator</param>
        /// <returns>Indicator 3.6.1 response in the form of data table</returns>
        public TabularDataInputModel BuildAnalyticalReport(Delegate translator, string indicatorSequence)
        {
            List<AccessUser> accessUserReports = GetAccessUserData(translator);

            DataSet ds = new DataSet();

            DataTable dt = AnalyticalOutputHelper.GetDataTable(typeof(AccessUser), accessUserReports, indicatorSequence, translator);

            dt.Rows.Add();

            string pecentage = CalculateNullPercentageInArray();

            dt.Rows.Add(translator.DynamicInvoke(AnalyticalOutputConstants.ProportionOfHealthSystemLevels_3_6_1).ToString(), pecentage);

            ds.Tables.Add(dt);

            TabularDataInputModel tabularData = new TabularDataInputModel
            {
                SheetName = indicatorSequence,
                Tables = ds
            };

            return tabularData;
        }

        /// <summary>
        /// Get Lits of Access user data
        /// </summary>
        /// <param name="translator">Delegate object which is used for translation</param>  
        /// <returns>List of Access user data</returns>
        private List<AccessUser> GetAccessUserData(Delegate translator)
        {
            List<string> variableName = new List<string>(new string[] { translator.DynamicInvoke(AnalyticalOutputConstants.RolesOfIndividuals_3_6_1).ToString(), translator.DynamicInvoke(AnalyticalOutputConstants.MethodOfAccess_3_6_1).ToString(), translator.DynamicInvoke(AnalyticalOutputConstants.WhatCanBeAccessed_3_6_1).ToString(), translator.DynamicInvoke(AnalyticalOutputConstants.CanDataBeAccessed_3_6_1).ToString() });

            int index = 0;

            AccessUsers.ForEach(data =>
            {
                data.Name = variableName[index];
                index++;
            });

            return AccessUsers;
        }
        /// <summary>
        /// Get the percentage value based on access control settings for national, subnational, and service delivery levels
        /// </summary>
        /// <returns>Percentage</returns>
        private string CalculateNullPercentageInArray()
        {
            List<bool> accessValues = new List<bool> { CanAccessNational, CanAccessSubNational, CanAccessServiceDelivery };

            int trueValueCount = accessValues.Count(value => value == true);
            int totalCount = accessValues.Count;

            int percentage = AnalyticalOutputHelper.CalculatePercentage(totalCount, trueValueCount);

            return $"{percentage}%";
        }
    }

    /// <summary>
    /// Contains details of AccessUsers
    /// </summary>
    public class AccessUser
    {
        [TableColumn(Name = "Name", TranslationKey = "DRObjective_3_Responses.Indicator_3_6_1.DataAccessDetails", Width = Common.Width300, Order = 1)]
        public string Name { get; set; }

        public string DataAccessDetails { get; set; }

        [TableColumn(Name = "National", TranslationKey = "DRObjective_3_Responses.Indicator_3_6_1.National", Width = Common.Width200, Order = 3)]
        public string National { get; set; }

        [TableColumn(Name = "SubNational", TranslationKey = "DRObjective_3_Responses.Indicator_3_6_1.Subnational", Width = Common.Width200, Order = 4)]
        public string SubNational { get; set; }

        [TableColumn(Name = "ServiceDelivery", TranslationKey = "DRObjective_3_Responses.Indicator_3_6_1.ServiceDelivery", Width = Common.Width200, Order = 5)]
        public string ServiceDelivery { get; set; }
    }

    public class AccessUserReport : AccessUser
    {
        public AccessUserReport(string name, string dataAccessDetails, string national, string subNational, string serviceDelivery)
        {
            Name = name;
            DataAccessDetails = dataAccessDetails;
            National = national;
            SubNational = subNational;
            ServiceDelivery = serviceDelivery;
        }
    }
}
