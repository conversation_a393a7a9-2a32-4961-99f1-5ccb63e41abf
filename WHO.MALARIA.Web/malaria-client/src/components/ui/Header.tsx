﻿import { useState, useEffect } from "react";
import { NavLink, useNavigate } from "react-router-dom";
import { Link } from "@mui/material";
import { useTranslation } from "react-i18next";

import ChooseLanguage from "./ChooseLanguage";
import GlobeIcon from "./GlobeIcon";
import <PERSON><PERSON><PERSON> from "./icons/WHOLogo";
import { authService } from "../../services/authService";
import { CurrentUserModel, UserCountry } from "../../models/ProfileModel";
import Modal from "../controls/Modal";
import classes from "./Header.module.scss";
import Profile from "../user/Profile";
import { UtilityHelper } from "../../utils/UtilityHelper";
import { UserRoleEnum } from "../../models/Enums";
import ProfileDefaultLogo from "../../images/profile-default-big.svg";
import classNames from "classnames";
import cookies from "js-cookie";
import { KeyValuePair } from "../../models/DeskReview/KeyValueType";
import Dropdown from "../controls/Dropdown";
import MultiSelectModel from "../../models/MultiSelectModel";
import { userService } from "../../services/userService";
import { Constants } from "../../models/Constants";
import { constants } from "fs";
import { useDispatch, useSelector } from "react-redux";
import { showSelectedCountry } from "../../redux/ducks/selected-country";

function Header() {
  const navigate = useNavigate();
  const [open, setOpen] = useState(false);
  const [openNewuserDialog, setOpenNewUserDialog] = useState(false);
  const currentLanguageCode = cookies.get("i18next") || "en";
  const dispatch = useDispatch();

  const [showDialogLogo, setShowDialogLogo] = useState(true);
  const currentUser: CurrentUserModel = authService.getCurrentUser();
  const isAuthenticated = authService.isAuthenticated();
  const [activeUserCountries, setActiveUserCountries] = useState<
    Array<UserCountry>
  >([]);
  const [userCountries, setUserCountries] = useState<
    Array<KeyValuePair<string, string>>
  >([]);
  const [countryName, setCountryName] = useState<string>("");

  const selectedCountryId = sessionStorage.getItem(
    Constants.SessionStorageKey.SELECTED_COUNTRY
  );
  const selectedUserType = sessionStorage.getItem(
    Constants.SessionStorageKey.SELECTED_USER_TYPE
  );

  const [selectedCountry, setSelectedCountry] = useState<string>(
    selectedCountryId || ""
  );
  const [selectedRole, setSelectedRole] = useState<string>(
    selectedUserType || ""
  );

  const isSelectedCountry: boolean = useSelector(
    (state: any) => state.countrySelection.isSelectedCountry
  );

  useEffect(() => {
    setOpenNewUserDialog(currentUser && currentUser.isNewUser);
  }, []);

  useEffect(() => {
    if (
      currentUser.userType !== UserRoleEnum.WHOAdmin &&
      activeUserCountries.length === 0
    ) {
      bindUserCountries();
    }
  }, [currentUser, isSelectedCountry]);

  useEffect(() => {
    if (
      !authService.canRenderPrivateRoute() &&
      window.location.href.indexOf("user/invitation") === -1 &&
      window.location.href.indexOf("deactivateduser") === -1 &&
      window.location.href.indexOf("inActivatedUser") === -1
    ) {
      navigate("/", { replace: true });
    }
  }, []); // Empty dependency array to run only once on mount

  const { t } = useTranslation();

  // Helps generating the url on the fly to bind it to user management
  const navitateTo = () => {
    // check if current user is 'SuperManager' navigate to respective page
    const isWHOAdminUser = UtilityHelper.isInRole([UserRoleEnum.WHOAdmin]);
    if (isWHOAdminUser) return "/super-managers";

    // check if current's role is 'Manager' then navigate to respective screen
    const isSuperManager = UtilityHelper.isInRole([UserRoleEnum.SuperManager]);
    if (isSuperManager) return "/managers";

    //Tracking user management event in analytics
    UtilityHelper.onEventAnalytics(
      "User management",
      "User management",
      "User management"
    );

    return "";
  };

  //check if current role is 'WhoAdmin' then navigate to respective page
  const navitateToAnalytics = () => {
    const isWHOAdminUser = UtilityHelper.isInRole([UserRoleEnum.WHOAdmin]);
    if (isWHOAdminUser) return "/analytics";

    return "";
  };

  //check if current role is 'WhoAdmin' then navigate to respective page
  const navitateToUploadDocument = () => {
    const isWHOAdminUser = UtilityHelper.isInRole([UserRoleEnum.WHOAdmin]);
    if (isWHOAdminUser) return "/upload-document";

    return "";
  };

  const showUserManagementMenu = UtilityHelper.isInRole([
    UserRoleEnum.SuperManager,
    UserRoleEnum.WHOAdmin,
  ]);

  const showAnalyticsMenu = UtilityHelper.isInRole([UserRoleEnum.WHOAdmin]);

  const showHealthFacilitiesMenu = UtilityHelper.isInRole([
    UserRoleEnum.SuperManager,
  ]);

  const showUploadDocumentMenu = UtilityHelper.isInRole([
    UserRoleEnum.WHOAdmin,
  ]);

  //check if current role is 'Super Manager' then navigate to respective page
  const navitateToHealthFacilities = () => {
    const isSuperManagerUser = UtilityHelper.isInRole([
      UserRoleEnum.SuperManager,
    ]);
    if (isSuperManagerUser) return "/health-facilities";

    return "";
  };

  // Bind user countries when user 1st time login and set default country and role
  const bindUserCountries = () => {
    const activeUserCountries = sessionStorage.getItem(
      Constants.SessionStorageKey.ACTIVE_USER_COUNTRIES
    );
    if (activeUserCountries !== null && activeUserCountries?.length) {
      const countries: Array<UserCountry> = JSON.parse(activeUserCountries);
      const userCountries: Array<KeyValuePair<string, string>> = countries.map(
        (value: UserCountry) => {
          return { key: value.id, value: value.name };
        }
      );
      setUserCountries(userCountries);
      setActiveUserCountries(countries);
      sessionStorage.setItem(
        Constants.SessionStorageKey.SELECTED_COUNTRY,
        countries[0]?.id
      );
      sessionStorage.setItem(
        Constants.SessionStorageKey.SELECTED_COUNTRY_NAME,
        countries[0]?.name
      );
      sessionStorage.setItem(
        Constants.SessionStorageKey.SELECTED_USER_TYPE,
        countries[0]?.userType.toString()
      );
      setSelectedCountry(countries[0]?.id);
      setCountryName(countries[0]?.name);
      setSelectedRole(countries[0]?.userType.toString());
      dispatch(showSelectedCountry(false));
    }
  };

  // Triggers whenever user tries to modify the value of country
  const onChangeCountry = (id: string) => {
    if (id && id !== selectedCountry) {
      setSelectedCountry(id);

      const countryRole = activeUserCountries.find(
        (value: UserCountry) => value.id === id
      );

      // selected country wise show role of that user and set country selected in database
      if (countryRole !== undefined) {
        sessionStorage.setItem(
          Constants.SessionStorageKey.SELECTED_COUNTRY,
          countryRole?.id
        );
        sessionStorage.setItem(
          Constants.SessionStorageKey.SELECTED_COUNTRY_NAME,
          countryRole?.name
        );
        sessionStorage.setItem(
          Constants.SessionStorageKey.SELECTED_USER_TYPE,
          countryRole?.userType.toString()
        );
        setSelectedRole(countryRole?.userType.toString());
        setCountryName(countryRole?.name);
        userService
          .updateUserCountryDeafultOnLandingPage(
            countryRole?.id,
            countryRole?.userType
          )
          .then(response => {
            if (response) {
              userService.getUserActiveCountries().then(response => {
                sessionStorage.setItem(
                  Constants.SessionStorageKey.ACTIVE_USER_COUNTRIES,
                  JSON.stringify(response)
                );
              });
              navigate("/");
            }
          });
      }
    }
  };

  return (
    <>
      {(currentUser || openNewuserDialog) && (
        <Modal
          open={openNewuserDialog}
          title='Profile'
          onEscPress={false}
          onDialogClose={
            currentUser.userId ? () => setOpenNewUserDialog(false) : undefined
          }
        >
          <Profile
            registerUser={false}
            onCancel={() => setOpenNewUserDialog(false)}
          />
        </Modal>
      )}

      <header className={classNames("header-section", classes.headerWrapper)}>
        <nav
          className={
            isAuthenticated
              ? "navbar navbar-expand-md navbar-dark fixed-top bg-primary p-0"
              : "navbar navbar-expand-md navbar-dark fixed-top bg-primary"
          }
        >
          <button
            className='navbar-toggler'
            type='button'
            data-bs-toggle='collapse'
            data-bs-target='#appNavbarToggler'
            aria-controls='appNavbarToggler'
            aria-expanded='false'
            aria-label='Toggle navigation'
          >
            <span className='navbar-toggler-icon'></span>
          </button>
          <div className='collapse navbar-collapse' id='appNavbarToggler'>
            <div className={classes.navbarBrand}>
              <NavLink to={isAuthenticated ? "/Dashboard" : "/"}>
                <WHOLogo className={classes.brandLogo} />
              </NavLink>
              <NavLink className="mr-0" to={isAuthenticated ? "/Dashboard" : "/"}>
                <span className="app-logo-text text-uppercase">
                  {t("translation:app.MalariaAssessmentToolkit")}
                </span>
              </NavLink>
            </div>

            <div className='navbar-collapse collapse w-100 justify-content-center main-menu'>
              <ul className='navbar-nav mx-auto'>
                {isAuthenticated &&
                  (currentUser.userType !== UserRoleEnum.WhoViewer ||
                    userCountries.length > 0) && (
                    <>
                      <li className='nav-item'>
                        <NavLink
                          className={({ isActive }) =>
                            isActive ? "nav-link active" : "nav-link"
                          }
                          to={isAuthenticated ? "/Dashboard" : "/"}
                        >
                          {t("Menus.Dashboard")}
                        </NavLink>
                      </li>

                      <li className='nav-item'>
                        <NavLink
                          className={({ isActive }) =>
                            isActive ? "nav-link active" : "nav-link"
                          }
                          to={"/assessments"}
                        >
                          {t("Assessment.Assessments")}
                        </NavLink>
                      </li>
                      {(showUserManagementMenu ||
                        showAnalyticsMenu ||
                        showHealthFacilitiesMenu) && (
                        <li className='nav-item dropdown header-dropdown'>
                          <a
                            className='nav-link dropdown-toggle'
                            href='#'
                            id='navbarDropdown'
                            role='button'
                            data-bs-toggle='dropdown'
                            aria-expanded='false'
                          >
                            {t("Menus.Setup")}
                          </a>
                          <ul
                            className='dropdown-menu dropdown-menu-end'
                            aria-labelledby='navbarDropdown'
                          >
                            {isAuthenticated && (
                              <>
                                {showUserManagementMenu && (
                                  <li className='nav-item dropdown-item'>
                                    <NavLink
                                      className={({ isActive }) =>
                                        isActive
                                          ? "nav-link active"
                                          : "nav-link"
                                      }
                                      to={navitateTo()}
                                    >
                                      {t("Menus.UserManagement")}
                                    </NavLink>
                                  </li>
                                )}
                              </>
                            )}
                            {isAuthenticated && (
                              <>
                                {showAnalyticsMenu && (
                                  <li className='nav-item dropdown-item'>
                                    <NavLink
                                      className={({ isActive }) =>
                                        isActive
                                          ? "nav-link active"
                                          : "nav-link"
                                      }
                                      to={navitateToAnalytics()}
                                    >
                                      {t("Menus.Analytics")}
                                    </NavLink>
                                  </li>
                                )}
                              </>
                            )}
                            {isAuthenticated && (
                              <>
                                {showHealthFacilitiesMenu && (
                                  <li className='nav-item dropdown-item'>
                                    <NavLink
                                      className={({ isActive }) =>
                                        isActive
                                          ? "nav-link active"
                                          : "nav-link"
                                      }
                                      to={navitateToHealthFacilities()}
                                    >
                                      {t("Menus.HealthFacilityManagement")}
                                    </NavLink>
                                  </li>
                                )}
                              </>
                            )}
                            {isAuthenticated && (
                              <>
                                {showUploadDocumentMenu && (
                                  <li className='nav-item dropdown-item'>
                                    <NavLink
                                      className={({ isActive }) =>
                                        isActive
                                          ? "nav-link active"
                                          : "nav-link"
                                      }
                                      to={navitateToUploadDocument()}
                                    >
                                      {t("Menus.UploadDocument")}
                                    </NavLink>
                                  </li>
                                )}
                              </>
                            )}
                          </ul>
                        </li>
                      )}
                    </>
                  )}
              </ul>
            </div>

            <div className='collapse navbar-collapse flex-column ml-lg-0 ml-3'>
              <ul className='d-flex ml-auto navbar-nav mb-2 mb-lg-0 pe-3 justify-content-between text-white'>
                <li className='nav-item dropdown language-selection'>
                  <a
                    className='nav-link dropdown-toggle'
                    id='navbarDropdownMenuLink'
                    role='button'
                    data-bs-toggle='dropdown'
                    aria-expanded='false'
                  >
                    <GlobeIcon /> {currentLanguageCode}
                  </a>
                  <ChooseLanguage />
                </li>
              </ul>

              <ul className='d-flex ml-auto navbar-nav mb-2 mb-lg-0 pe-3 justify-content-between text-white'>
                <li className='country-selection'>
                  {isAuthenticated &&
                    currentUser.userType !== UserRoleEnum.WHOAdmin &&
                    userCountries.length > 0 && (
                      <ul className='list-unstyled d-flex flex-row-reverse'>
                        <li className='country-selection-dropdown nav-item dropdown'>
                          <a
                            className='nav-link dropdown-toggle px-0'
                            href='#'
                            id='navbarDropdown'
                            role='button'
                            data-bs-toggle='dropdown'
                            aria-expanded='false'
                          >
                            {countryName}
                          </a>
                          <ul
                            className='dropdown-menu dropdown-menu-end p-0'
                            aria-labelledby='navbarDropdown'
                          >
                            {userCountries.map(userCountry => (
                              <li
                                key={userCountry.key}
                                className='nav-item dropdown-item'
                              >
                                <a
                                  className='dropdown-item'
                                  onClick={() =>
                                    onChangeCountry(userCountry.key)
                                  }
                                  defaultValue={selectedCountry}
                                >
                                  {userCountry.value}
                                </a>
                              </li>
                            ))}
                          </ul>
                        </li>
                        <li className='nav-item dropdown country-role small'>
                          {UtilityHelper.getRoleName(+selectedRole)}
                        </li>
                      </ul>
                    )}

                  {isAuthenticated &&
                    currentUser.userType === UserRoleEnum.WHOAdmin && (
                      <li className='nav-item dropdown'>
                        {UtilityHelper.getRoleName(currentUser.userType)}
                      </li>
                    )}
                </li>

                <li className='nav-item ps-3'>
                  {isAuthenticated && (
                    <div className='app-profile-section  ps-3'>
                      <ul className='navbar-nav align-items-center d-flex'>
                        <li className='nav-item dropdown'>
                          <a
                            className='nav-link pr-0'
                            href='#'
                            role='button'
                            data-bs-toggle='dropdown'
                            aria-haspopup='true'
                            aria-expanded='false'
                          >
                            <div className='media d-flex align-items-center'>
                              <span className='avatar avatar-sm rounded-circle'>
                                <img
                                  alt='Image placeholder'
                                  src={ProfileDefaultLogo}
                                  width='28'
                                  height='28'
                                />
                              </span>
                              <div className='media-body ms-2 d-none d-lg-block'>
                                <span className='user-name mb-0 text-sm'>
                                  {currentUser.name}
                                </span>
                              </div>
                            </div>
                          </a>

                          <div className='dropdown-menu dropdown-menu-arrow dropdown-menu-end'>
                            <div className=' dropdown-header noti-title'>
                              <div className='media d-flex align-items-center'>
                                <span className='avatar avatar-sm rounded-circle'>
                                  <img
                                    alt='Image placeholder'
                                    src={ProfileDefaultLogo}
                                    width='28'
                                    height='28'
                                  />
                                </span>
                                <div className='media-body ms-3'>
                                  <span className='mb-0 text-sm user-name d-flex font-weight-bold'>
                                    {currentUser.name}
                                  </span>
                                </div>
                              </div>
                            </div>

                            {isAuthenticated && (
                              <a
                                className='dropdown-item'
                                onClick={() => setOpenNewUserDialog(true)}
                              >
                                {t("Menus.MyProfile")}
                              </a>
                            )}

                            <Link
                              className='dropdown-item'
                              onClick={() => authService.logout()}
                            >
                              {t("Menus.Logout")}
                            </Link>
                          </div>
                        </li>
                      </ul>
                    </div>
                  )}
                </li>
              </ul>
            </div>
          </div>
        </nav>
      </header>
    </>
  );
}

export default Header;
