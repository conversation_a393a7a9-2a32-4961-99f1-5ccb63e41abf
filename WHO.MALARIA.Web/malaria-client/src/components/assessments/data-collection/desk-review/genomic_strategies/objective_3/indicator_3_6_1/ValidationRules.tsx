﻿import { DataType } from "../../../../../../../models/Enums";
import { Constants } from "../../../../../../../models/Constants";
import ValidationRuleModel, {
  IValidationRuleProvider,
} from "../../../../../../../models/ValidationRuleModel";

/** Genomic 3.6.1 validation rules */
const ValidationRules: IValidationRuleProvider = {
  accessUsers: new ValidationRuleModel(DataType.ArrayOfObject, true),
  [`accessUsers[${Constants.Common.IndexSubstitute}].national`]:
    new ValidationRuleModel(
      DataType.String,
      true,
      `${Constants.Common.RootObjectNameSubstitute}.canAccessNational === true && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.accessUsers[${Constants.Common.IndexSubstitute}].national)`
    ),
  [`accessUsers[${Constants.Common.IndexSubstitute}].subNational`]:
    new ValidationRuleModel(
      DataType.String,
      true,
      `${Constants.Common.RootObjectNameSubstitute}.canAccessSubNational === true && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.accessUsers[${Constants.Common.IndexSubstitute}].subNational)`
    ),
  [`accessUsers[${Constants.Common.IndexSubstitute}].serviceDelivery`]:
    new ValidationRuleModel(
      DataType.String,
      true,
      `${Constants.Common.RootObjectNameSubstitute}.canAccessServiceDelivery === true && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.accessUsers[${Constants.Common.IndexSubstitute}].serviceDelivery)`
    ),
};

export default ValidationRules;
